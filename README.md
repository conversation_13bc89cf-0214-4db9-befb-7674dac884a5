# Auto SSL Certificate Manager

A comprehensive, intelligent SSL certificate management tool for Linux servers using Let's Encrypt. This tool provides an interactive menu-driven interface for generating, managing, and monitoring SSL certificates with advanced features like duplicate detection, health checks, and automatic backups.

## 🚀 Features

### Core Features
- **Interactive Menu System**: User-friendly numbered menu interface
- **Let's Encrypt Integration**: Automatic SSL certificate generation
- **Duplicate Detection**: Smart detection of existing certificates with override options
- **Certificate Health Monitoring**: Comprehensive health checks and status monitoring
- **Automatic Backup & Restore**: Built-in backup and restoration capabilities
- **Domain Validation**: Advanced domain format and accessibility validation
- **Certificate Renewal**: Automated and manual renewal options
- **Configuration Management**: Persistent settings and domain history

### Advanced Features
- **Multi-Domain Support**: Manage certificates for multiple domains
- **Staging Mode**: Test certificate generation without rate limits
- **Certificate Details Viewer**: Detailed certificate information display
- **Expiry Monitoring**: Track certificate expiration dates
- **Command Line Interface**: Both interactive and CLI modes
- **Color-Coded Output**: Beautiful, readable terminal output
- **Error Handling**: Robust error handling and user feedback

## 📋 Requirements

### System Requirements
- Linux-based operating system
- Python 3.6 or higher
- Root or sudo access (for certificate management)

### Dependencies
The script will automatically attempt to install required dependencies:
- `certbot` (Let's Encrypt client)
- `openssl` (SSL/TLS toolkit)
- `curl` (for testing)

### Manual Installation
```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install certbot openssl curl python3

# CentOS/RHEL
sudo yum install certbot openssl curl python3

# Fedora
sudo dnf install certbot openssl curl python3

# Arch Linux
sudo pacman -S certbot openssl curl python3
```

## 🛠️ Installation

1. **Download the script:**
```bash
wget https://raw.githubusercontent.com/your-repo/auto-ssl-manager/main/auto_ssl_manager.py
chmod +x auto_ssl_manager.py
```

2. **Or clone the repository:**
```bash
git clone https://github.com/your-repo/auto-ssl-manager.git
cd auto-ssl-manager
chmod +x auto_ssl_manager.py
```

## 🎯 Usage

### Interactive Mode
```bash
sudo python3 auto_ssl_manager.py
```

### Command Line Mode
```bash
# Generate certificate for a domain
sudo python3 auto_ssl_manager.py -d example.com -e <EMAIL>

# Check certificate status
sudo python3 auto_ssl_manager.py --check

# Renew certificates
sudo python3 auto_ssl_manager.py --renew

# Use staging environment (for testing)
sudo python3 auto_ssl_manager.py -d example.com -e <EMAIL> --staging

# Force regeneration of existing certificate
sudo python3 auto_ssl_manager.py -d example.com --force
```

### Command Line Options
- `-d, --domain`: Domain name for certificate generation
- `-e, --email`: Email address for Let's Encrypt registration
- `-s, --staging`: Use Let's Encrypt staging environment
- `-f, --force`: Force certificate generation even if exists
- `-c, --check`: Check certificate status and exit
- `-r, --renew`: Renew certificates and exit

## 📁 File Structure

The tool creates and manages the following files:
- `/root/server.crt` - SSL certificate file
- `/root/server.key` - Private key file
- `ssl_config.json` - Configuration settings
- `domains_history.json` - Domain management history
- `ssl_backup_*` - Backup directories (timestamped)

## 🎮 Menu Options

1. **Generate New SSL Certificate** - Create certificates for new domains
2. **Check Certificate Status** - View current certificate status and expiry
3. **Renew Existing Certificate** - Renew certificates manually
4. **View Certificate Details** - Display detailed certificate information
5. **Validate Domain** - Test domain accessibility and SSL status
6. **Backup Certificates** - Create timestamped backups
7. **Restore Certificates** - Restore from previous backups
8. **View Domains History** - See all managed domains and their status
9. **Configuration Settings** - Modify application settings
10. **Certificate Health Check** - Comprehensive certificate health analysis

## ⚙️ Configuration

The tool maintains configuration in `ssl_config.json`:
```json
{
  "email": "<EMAIL>",
  "cert_path": "/root/server.crt",
  "key_path": "/root/server.key",
  "auto_renew": true,
  "staging": false
}
```

## 🔒 Security Features

- **Duplicate Detection**: Prevents accidental certificate overwrites
- **Backup System**: Automatic backups before major operations
- **Validation Checks**: Domain format and accessibility validation
- **Health Monitoring**: Comprehensive certificate health checks
- **Secure Defaults**: Uses Let's Encrypt production environment by default

## 🚨 Troubleshooting

### Common Issues

1. **Permission Denied**
   - Solution: Run with sudo privileges

2. **Certbot Not Found**
   - Solution: The script will attempt automatic installation

3. **Domain Not Accessible**
   - Solution: Ensure domain points to your server and port 80 is open

4. **Certificate Generation Failed**
   - Solution: Check domain DNS settings and firewall configuration

### Debug Mode
Enable verbose output by modifying the script or checking logs in `/var/log/letsencrypt/`

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📞 Support

For support and questions, please open an issue on GitHub or contact the maintainer.

---

**Made with ❤️ for the SSL community**
