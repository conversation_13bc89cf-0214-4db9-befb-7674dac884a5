#!/usr/bin/env python3
"""
Auto SSL Certificate Manager
A comprehensive tool for managing SSL certificates with Let's Encrypt
"""

import os
import sys
import json
import subprocess
import datetime
import socket
import ssl
import re
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import argparse

class Colors:
    """ANSI color codes for terminal output"""
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

class SSLManager:
    def __init__(self):
        self.cert_path = "/root/server.crt"
        self.key_path = "/root/server.key"
        self.config_file = "ssl_config.json"
        self.domains_db = "domains_history.json"
        self.load_config()
        self.load_domains_history()
    
    def load_config(self):
        """Load configuration from file"""
        default_config = {
            "email": "",
            "cert_path": self.cert_path,
            "key_path": self.key_path,
            "auto_renew": True,
            "staging": False
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    self.config = json.load(f)
            except:
                self.config = default_config
        else:
            self.config = default_config
    
    def save_config(self):
        """Save configuration to file"""
        with open(self.config_file, 'w') as f:
            json.dump(self.config, f, indent=2)
    
    def load_domains_history(self):
        """Load domains history from file"""
        if os.path.exists(self.domains_db):
            try:
                with open(self.domains_db, 'r') as f:
                    self.domains_history = json.load(f)
            except:
                self.domains_history = {}
        else:
            self.domains_history = {}
    
    def save_domains_history(self):
        """Save domains history to file"""
        with open(self.domains_db, 'w') as f:
            json.dump(self.domains_history, f, indent=2)
    
    def print_banner(self):
        """Print application banner"""
        banner = f"""
{Colors.HEADER}╔══════════════════════════════════════════════════════════════╗
║                    Auto SSL Certificate Manager              ║
║                         Version 1.0                          ║
╚══════════════════════════════════════════════════════════════╝{Colors.ENDC}
        """
        print(banner)
    
    def print_menu(self):
        """Print main menu"""
        menu = f"""
{Colors.OKBLUE}┌─────────────────────────────────────────────────────────────┐
│                        MAIN MENU                            │
├─────────────────────────────────────────────────────────────┤{Colors.ENDC}
{Colors.OKCYAN}│ 1. Generate New SSL Certificate                             │
│ 2. Check Certificate Status                                 │
│ 3. Renew Existing Certificate                               │
│ 4. View Certificate Details                                 │
│ 5. Validate Domain                                          │
│ 6. Backup Certificates                                      │
│ 7. Restore Certificates                                     │
│ 8. View Domains History                                     │
│ 9. Configuration Settings                                   │
│ 10. Certificate Health Check                                │
│ 0. Exit                                                     │{Colors.ENDC}
{Colors.OKBLUE}└─────────────────────────────────────────────────────────────┘{Colors.ENDC}
        """
        print(menu)
    
    def validate_domain(self, domain: str) -> bool:
        """Validate domain format"""
        pattern = r'^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$'
        return re.match(pattern, domain) is not None
    
    def check_domain_accessibility(self, domain: str, port: int = 80) -> bool:
        """Check if domain is accessible"""
        try:
            socket.create_connection((domain, port), timeout=5)
            return True
        except:
            return False
    
    def get_certificate_info(self, domain: str, port: int = 443) -> Optional[Dict]:
        """Get SSL certificate information"""
        try:
            context = ssl.create_default_context()
            with socket.create_connection((domain, port), timeout=10) as sock:
                with context.wrap_socket(sock, server_hostname=domain) as ssock:
                    cert = ssock.getpeercert()
                    return {
                        'subject': dict(x[0] for x in cert['subject']),
                        'issuer': dict(x[0] for x in cert['issuer']),
                        'version': cert['version'],
                        'serial_number': cert['serialNumber'],
                        'not_before': cert['notBefore'],
                        'not_after': cert['notAfter'],
                        'san': cert.get('subjectAltName', [])
                    }
        except Exception as e:
            return None
    
    def check_certificate_expiry(self, cert_path: str) -> Optional[datetime.datetime]:
        """Check certificate expiry date"""
        try:
            result = subprocess.run([
                'openssl', 'x509', '-in', cert_path, '-noout', '-enddate'
            ], capture_output=True, text=True)

            if result.returncode == 0:
                date_str = result.stdout.strip().split('=')[1]
                return datetime.datetime.strptime(date_str, '%b %d %H:%M:%S %Y %Z')
        except:
            pass
        return None

    def install_certbot(self) -> bool:
        """Install certbot if not available"""
        try:
            subprocess.run(['certbot', '--version'], capture_output=True, check=True)
            return True
        except:
            print(f"{Colors.WARNING}Certbot not found. Installing...{Colors.ENDC}")
            try:
                # Try different installation methods
                install_commands = [
                    ['apt-get', 'update', '&&', 'apt-get', 'install', '-y', 'certbot'],
                    ['yum', 'install', '-y', 'certbot'],
                    ['snap', 'install', '--classic', 'certbot'],
                    ['pip3', 'install', 'certbot']
                ]

                for cmd in install_commands:
                    try:
                        subprocess.run(cmd, check=True)
                        print(f"{Colors.OKGREEN}Certbot installed successfully!{Colors.ENDC}")
                        return True
                    except:
                        continue

                print(f"{Colors.FAIL}Failed to install certbot automatically.{Colors.ENDC}")
                print("Please install certbot manually and try again.")
                return False
            except Exception as e:
                print(f"{Colors.FAIL}Installation failed: {e}{Colors.ENDC}")
                return False

    def generate_certificate(self, domain: str, force: bool = False) -> bool:
        """Generate SSL certificate using Let's Encrypt"""
        if not self.install_certbot():
            return False

        # Check if domain already exists
        if domain in self.domains_history and not force:
            last_generated = self.domains_history[domain].get('last_generated')
            print(f"{Colors.WARNING}Certificate for '{domain}' already exists!{Colors.ENDC}")
            print(f"Last generated: {last_generated}")

            choice = input(f"{Colors.OKCYAN}Do you want to regenerate? (y/N): {Colors.ENDC}").lower()
            if choice != 'y':
                return False

        # Validate domain
        if not self.validate_domain(domain):
            print(f"{Colors.FAIL}Invalid domain format: {domain}{Colors.ENDC}")
            return False

        # Check domain accessibility
        if not self.check_domain_accessibility(domain):
            print(f"{Colors.WARNING}Warning: Domain {domain} is not accessible on port 80{Colors.ENDC}")
            choice = input(f"{Colors.OKCYAN}Continue anyway? (y/N): {Colors.ENDC}").lower()
            if choice != 'y':
                return False

        # Prepare certbot command
        staging_flag = "--staging" if self.config.get('staging', False) else ""
        email_flag = f"--email {self.config['email']}" if self.config['email'] else "--register-unsafely-without-email"

        cmd = [
            'certbot', 'certonly',
            '--standalone',
            '--non-interactive',
            '--agree-tos',
            email_flag,
            '-d', domain,
            '--cert-path', self.cert_path,
            '--key-path', self.key_path
        ]

        if staging_flag:
            cmd.append(staging_flag)

        print(f"{Colors.OKCYAN}Generating SSL certificate for {domain}...{Colors.ENDC}")

        try:
            # Filter out empty strings from cmd
            cmd = [arg for arg in cmd if arg]
            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                print(f"{Colors.OKGREEN}✓ Certificate generated successfully!{Colors.ENDC}")

                # Update domains history
                self.domains_history[domain] = {
                    'last_generated': datetime.datetime.now().isoformat(),
                    'status': 'active',
                    'cert_path': self.cert_path,
                    'key_path': self.key_path
                }
                self.save_domains_history()

                # Copy certificates to specified paths
                self.copy_certificates(domain)
                return True
            else:
                print(f"{Colors.FAIL}✗ Certificate generation failed!{Colors.ENDC}")
                print(f"Error: {result.stderr}")
                return False

        except Exception as e:
            print(f"{Colors.FAIL}Error generating certificate: {e}{Colors.ENDC}")
            return False

    def copy_certificates(self, domain: str):
        """Copy certificates to specified paths"""
        try:
            # Find the generated certificates
            cert_dir = f"/etc/letsencrypt/live/{domain}"

            if os.path.exists(f"{cert_dir}/fullchain.pem"):
                subprocess.run(['cp', f"{cert_dir}/fullchain.pem", self.cert_path])
                subprocess.run(['cp', f"{cert_dir}/privkey.pem", self.key_path])
                print(f"{Colors.OKGREEN}Certificates copied to:{Colors.ENDC}")
                print(f"  Certificate: {self.cert_path}")
                print(f"  Private Key: {self.key_path}")
            else:
                print(f"{Colors.WARNING}Certificate files not found in expected location{Colors.ENDC}")

        except Exception as e:
            print(f"{Colors.FAIL}Error copying certificates: {e}{Colors.ENDC}")

    def check_certificate_status(self):
        """Check current certificate status"""
        print(f"{Colors.HEADER}Certificate Status Check{Colors.ENDC}")
        print("=" * 50)

        if not os.path.exists(self.cert_path):
            print(f"{Colors.FAIL}✗ Certificate file not found: {self.cert_path}{Colors.ENDC}")
            return

        if not os.path.exists(self.key_path):
            print(f"{Colors.FAIL}✗ Private key file not found: {self.key_path}{Colors.ENDC}")
            return

        # Check expiry
        expiry_date = self.check_certificate_expiry(self.cert_path)
        if expiry_date:
            days_left = (expiry_date - datetime.datetime.now()).days

            if days_left > 30:
                status_color = Colors.OKGREEN
                status = "✓ Valid"
            elif days_left > 7:
                status_color = Colors.WARNING
                status = "⚠ Expires Soon"
            else:
                status_color = Colors.FAIL
                status = "✗ Expired/Critical"

            print(f"{status_color}{status}{Colors.ENDC}")
            print(f"Expires: {expiry_date.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"Days remaining: {days_left}")
        else:
            print(f"{Colors.FAIL}✗ Could not read certificate expiry{Colors.ENDC}")

    def view_certificate_details(self):
        """View detailed certificate information"""
        print(f"{Colors.HEADER}Certificate Details{Colors.ENDC}")
        print("=" * 50)

        if not os.path.exists(self.cert_path):
            print(f"{Colors.FAIL}Certificate file not found: {self.cert_path}{Colors.ENDC}")
            return

        try:
            result = subprocess.run([
                'openssl', 'x509', '-in', self.cert_path, '-text', '-noout'
            ], capture_output=True, text=True)

            if result.returncode == 0:
                print(result.stdout)
            else:
                print(f"{Colors.FAIL}Error reading certificate details{Colors.ENDC}")

        except Exception as e:
            print(f"{Colors.FAIL}Error: {e}{Colors.ENDC}")

    def renew_certificate(self, domain: str = None):
        """Renew existing certificate"""
        print(f"{Colors.HEADER}Certificate Renewal{Colors.ENDC}")
        print("=" * 50)

        if not self.install_certbot():
            return False

        try:
            if domain:
                cmd = ['certbot', 'renew', '--cert-name', domain]
            else:
                cmd = ['certbot', 'renew']

            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                print(f"{Colors.OKGREEN}✓ Certificate renewal completed!{Colors.ENDC}")
                print(result.stdout)
                return True
            else:
                print(f"{Colors.FAIL}✗ Certificate renewal failed!{Colors.ENDC}")
                print(result.stderr)
                return False

        except Exception as e:
            print(f"{Colors.FAIL}Error renewing certificate: {e}{Colors.ENDC}")
            return False

    def backup_certificates(self):
        """Backup current certificates"""
        backup_dir = f"ssl_backup_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"

        try:
            os.makedirs(backup_dir, exist_ok=True)

            if os.path.exists(self.cert_path):
                subprocess.run(['cp', self.cert_path, f"{backup_dir}/server.crt"])

            if os.path.exists(self.key_path):
                subprocess.run(['cp', self.key_path, f"{backup_dir}/server.key"])

            # Backup configuration
            if os.path.exists(self.config_file):
                subprocess.run(['cp', self.config_file, f"{backup_dir}/ssl_config.json"])

            if os.path.exists(self.domains_db):
                subprocess.run(['cp', self.domains_db, f"{backup_dir}/domains_history.json"])

            print(f"{Colors.OKGREEN}✓ Certificates backed up to: {backup_dir}{Colors.ENDC}")
            return True

        except Exception as e:
            print(f"{Colors.FAIL}Backup failed: {e}{Colors.ENDC}")
            return False

    def restore_certificates(self):
        """Restore certificates from backup"""
        print(f"{Colors.HEADER}Certificate Restoration{Colors.ENDC}")
        print("=" * 50)

        # List available backups
        backup_dirs = [d for d in os.listdir('.') if d.startswith('ssl_backup_')]

        if not backup_dirs:
            print(f"{Colors.WARNING}No backup directories found{Colors.ENDC}")
            return False

        print("Available backups:")
        for i, backup_dir in enumerate(backup_dirs, 1):
            print(f"{i}. {backup_dir}")

        try:
            choice = int(input(f"{Colors.OKCYAN}Select backup to restore (1-{len(backup_dirs)}): {Colors.ENDC}"))
            if 1 <= choice <= len(backup_dirs):
                selected_backup = backup_dirs[choice - 1]

                # Restore files
                backup_cert = f"{selected_backup}/server.crt"
                backup_key = f"{selected_backup}/server.key"

                if os.path.exists(backup_cert):
                    subprocess.run(['cp', backup_cert, self.cert_path])

                if os.path.exists(backup_key):
                    subprocess.run(['cp', backup_key, self.key_path])

                print(f"{Colors.OKGREEN}✓ Certificates restored from {selected_backup}{Colors.ENDC}")
                return True
            else:
                print(f"{Colors.FAIL}Invalid selection{Colors.ENDC}")
                return False

        except ValueError:
            print(f"{Colors.FAIL}Invalid input{Colors.ENDC}")
            return False
        except Exception as e:
            print(f"{Colors.FAIL}Restoration failed: {e}{Colors.ENDC}")
            return False

    def view_domains_history(self):
        """View domains history"""
        print(f"{Colors.HEADER}Domains History{Colors.ENDC}")
        print("=" * 70)

        if not self.domains_history:
            print(f"{Colors.WARNING}No domains in history{Colors.ENDC}")
            return

        print(f"{'Domain':<30} {'Last Generated':<20} {'Status':<10}")
        print("-" * 70)

        for domain, info in self.domains_history.items():
            last_gen = info.get('last_generated', 'Unknown')
            if last_gen != 'Unknown':
                try:
                    dt = datetime.datetime.fromisoformat(last_gen)
                    last_gen = dt.strftime('%Y-%m-%d %H:%M')
                except:
                    pass

            status = info.get('status', 'Unknown')
            status_color = Colors.OKGREEN if status == 'active' else Colors.WARNING

            print(f"{domain:<30} {last_gen:<20} {status_color}{status:<10}{Colors.ENDC}")

    def configure_settings(self):
        """Configure application settings"""
        print(f"{Colors.HEADER}Configuration Settings{Colors.ENDC}")
        print("=" * 50)

        print(f"Current settings:")
        print(f"1. Email: {self.config.get('email', 'Not set')}")
        print(f"2. Certificate Path: {self.config.get('cert_path', self.cert_path)}")
        print(f"3. Private Key Path: {self.config.get('key_path', self.key_path)}")
        print(f"4. Auto Renew: {self.config.get('auto_renew', True)}")
        print(f"5. Staging Mode: {self.config.get('staging', False)}")
        print("6. Save and Exit")

        while True:
            try:
                choice = input(f"{Colors.OKCYAN}Select setting to modify (1-6): {Colors.ENDC}")

                if choice == '1':
                    email = input("Enter email address: ")
                    self.config['email'] = email
                elif choice == '2':
                    cert_path = input(f"Enter certificate path (current: {self.config.get('cert_path', self.cert_path)}): ")
                    if cert_path:
                        self.config['cert_path'] = cert_path
                        self.cert_path = cert_path
                elif choice == '3':
                    key_path = input(f"Enter private key path (current: {self.config.get('key_path', self.key_path)}): ")
                    if key_path:
                        self.config['key_path'] = key_path
                        self.key_path = key_path
                elif choice == '4':
                    auto_renew = input("Enable auto renew? (y/N): ").lower() == 'y'
                    self.config['auto_renew'] = auto_renew
                elif choice == '5':
                    staging = input("Enable staging mode? (y/N): ").lower() == 'y'
                    self.config['staging'] = staging
                elif choice == '6':
                    self.save_config()
                    print(f"{Colors.OKGREEN}✓ Configuration saved{Colors.ENDC}")
                    break
                else:
                    print(f"{Colors.FAIL}Invalid choice{Colors.ENDC}")

            except KeyboardInterrupt:
                break

    def certificate_health_check(self):
        """Perform comprehensive certificate health check"""
        print(f"{Colors.HEADER}Certificate Health Check{Colors.ENDC}")
        print("=" * 50)

        checks = []

        # Check if certificate files exist
        cert_exists = os.path.exists(self.cert_path)
        key_exists = os.path.exists(self.key_path)

        checks.append(("Certificate file exists", cert_exists))
        checks.append(("Private key file exists", key_exists))

        if cert_exists and key_exists:
            # Check certificate validity
            expiry_date = self.check_certificate_expiry(self.cert_path)
            if expiry_date:
                days_left = (expiry_date - datetime.datetime.now()).days
                checks.append(("Certificate not expired", days_left > 0))
                checks.append(("Certificate valid for >7 days", days_left > 7))
                checks.append(("Certificate valid for >30 days", days_left > 30))

            # Check certificate and key match
            try:
                cert_result = subprocess.run([
                    'openssl', 'x509', '-noout', '-modulus', '-in', self.cert_path
                ], capture_output=True, text=True)

                key_result = subprocess.run([
                    'openssl', 'rsa', '-noout', '-modulus', '-in', self.key_path
                ], capture_output=True, text=True)

                if cert_result.returncode == 0 and key_result.returncode == 0:
                    cert_modulus = cert_result.stdout.strip()
                    key_modulus = key_result.stdout.strip()
                    checks.append(("Certificate and key match", cert_modulus == key_modulus))
                else:
                    checks.append(("Certificate and key match", False))

            except:
                checks.append(("Certificate and key match", False))

        # Display results
        print()
        for check_name, result in checks:
            status = f"{Colors.OKGREEN}✓{Colors.ENDC}" if result else f"{Colors.FAIL}✗{Colors.ENDC}"
            print(f"{status} {check_name}")

        # Overall health score
        passed = sum(1 for _, result in checks if result)
        total = len(checks)
        score = (passed / total) * 100

        print(f"\n{Colors.HEADER}Health Score: {score:.1f}% ({passed}/{total} checks passed){Colors.ENDC}")

        if score == 100:
            print(f"{Colors.OKGREEN}🎉 Certificate is in perfect health!{Colors.ENDC}")
        elif score >= 80:
            print(f"{Colors.WARNING}⚠️  Certificate has minor issues{Colors.ENDC}")
        else:
            print(f"{Colors.FAIL}🚨 Certificate has serious issues that need attention{Colors.ENDC}")

    def validate_domain_menu(self):
        """Interactive domain validation"""
        print(f"{Colors.HEADER}Domain Validation{Colors.ENDC}")
        print("=" * 50)

        domain = input(f"{Colors.OKCYAN}Enter domain to validate: {Colors.ENDC}").strip()

        if not domain:
            print(f"{Colors.FAIL}No domain entered{Colors.ENDC}")
            return

        print(f"\nValidating domain: {domain}")
        print("-" * 30)

        # Format validation
        format_valid = self.validate_domain(domain)
        print(f"{'✓' if format_valid else '✗'} Domain format: {'Valid' if format_valid else 'Invalid'}")

        if not format_valid:
            return

        # Accessibility check
        accessible = self.check_domain_accessibility(domain, 80)
        print(f"{'✓' if accessible else '✗'} HTTP accessibility: {'Accessible' if accessible else 'Not accessible'}")

        # SSL check
        ssl_info = self.get_certificate_info(domain)
        if ssl_info:
            print(f"✓ HTTPS accessibility: Accessible")
            print(f"  Subject: {ssl_info['subject'].get('commonName', 'N/A')}")
            print(f"  Issuer: {ssl_info['issuer'].get('organizationName', 'N/A')}")
            print(f"  Expires: {ssl_info['not_after']}")
        else:
            print(f"✗ HTTPS accessibility: Not accessible or no SSL")

    def run(self):
        """Main application loop"""
        self.print_banner()

        while True:
            try:
                self.print_menu()
                choice = input(f"{Colors.OKCYAN}Select an option (0-10): {Colors.ENDC}").strip()

                if choice == '1':
                    domain = input(f"{Colors.OKCYAN}Enter domain name: {Colors.ENDC}").strip()
                    if domain:
                        self.generate_certificate(domain)
                    else:
                        print(f"{Colors.FAIL}No domain entered{Colors.ENDC}")

                elif choice == '2':
                    self.check_certificate_status()

                elif choice == '3':
                    domain = input(f"{Colors.OKCYAN}Enter domain to renew (leave empty for all): {Colors.ENDC}").strip()
                    self.renew_certificate(domain if domain else None)

                elif choice == '4':
                    self.view_certificate_details()

                elif choice == '5':
                    self.validate_domain_menu()

                elif choice == '6':
                    self.backup_certificates()

                elif choice == '7':
                    self.restore_certificates()

                elif choice == '8':
                    self.view_domains_history()

                elif choice == '9':
                    self.configure_settings()

                elif choice == '10':
                    self.certificate_health_check()

                elif choice == '0':
                    print(f"{Colors.OKGREEN}Thank you for using Auto SSL Manager!{Colors.ENDC}")
                    break

                else:
                    print(f"{Colors.FAIL}Invalid option. Please try again.{Colors.ENDC}")

                input(f"\n{Colors.OKCYAN}Press Enter to continue...{Colors.ENDC}")

            except KeyboardInterrupt:
                print(f"\n{Colors.WARNING}Operation cancelled by user{Colors.ENDC}")
                break
            except Exception as e:
                print(f"{Colors.FAIL}An error occurred: {e}{Colors.ENDC}")
                input(f"{Colors.OKCYAN}Press Enter to continue...{Colors.ENDC}")

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='Auto SSL Certificate Manager')
    parser.add_argument('--domain', '-d', help='Domain name for certificate generation')
    parser.add_argument('--email', '-e', help='Email address for Let\'s Encrypt registration')
    parser.add_argument('--staging', '-s', action='store_true', help='Use Let\'s Encrypt staging environment')
    parser.add_argument('--force', '-f', action='store_true', help='Force certificate generation even if exists')
    parser.add_argument('--check', '-c', action='store_true', help='Check certificate status and exit')
    parser.add_argument('--renew', '-r', action='store_true', help='Renew certificates and exit')

    args = parser.parse_args()

    ssl_manager = SSLManager()

    # Handle command line arguments
    if args.email:
        ssl_manager.config['email'] = args.email
        ssl_manager.save_config()

    if args.staging:
        ssl_manager.config['staging'] = True
        ssl_manager.save_config()

    if args.check:
        ssl_manager.check_certificate_status()
        return

    if args.renew:
        ssl_manager.renew_certificate()
        return

    if args.domain:
        success = ssl_manager.generate_certificate(args.domain, args.force)
        if success:
            print(f"{Colors.OKGREEN}Certificate generated successfully for {args.domain}{Colors.ENDC}")
        else:
            print(f"{Colors.FAIL}Failed to generate certificate for {args.domain}{Colors.ENDC}")
        return

    # Run interactive mode
    ssl_manager.run()

if __name__ == "__main__":
    main()
