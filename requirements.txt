# Auto SSL Manager Requirements
# Core dependencies for SSL certificate management

# No external Python packages required - uses only standard library
# However, system dependencies are required:

# System Requirements (install via package manager):
# - certbot (Let's Encrypt client)
# - openssl (SSL/TLS toolkit)
# - curl (for testing)

# Installation commands for different systems:
# Ubuntu/Debian: sudo apt-get install certbot openssl curl
# CentOS/RHEL: sudo yum install certbot openssl curl
# Fedora: sudo dnf install certbot openssl curl
# Arch Linux: sudo pacman -S certbot openssl curl

# Optional: For automatic certbot installation
# pip install certbot

# Note: This script is designed to work with Python 3.6+
# and uses only standard library modules for maximum compatibility
