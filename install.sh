#!/bin/bash

# Auto SSL Manager Installation Script
# This script installs all required dependencies and sets up the SSL manager

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        print_error "This script must be run as root (use sudo)"
        exit 1
    fi
}

# Detect OS
detect_os() {
    if [[ -f /etc/os-release ]]; then
        . /etc/os-release
        OS=$NAME
        VER=$VERSION_ID
    else
        print_error "Cannot detect operating system"
        exit 1
    fi
    
    print_status "Detected OS: $OS $VER"
}

# Install dependencies based on OS
install_dependencies() {
    print_status "Installing dependencies..."
    
    case $OS in
        *"Ubuntu"*|*"Debian"*)
            apt-get update
            apt-get install -y python3 python3-pip certbot openssl curl wget
            ;;
        *"CentOS"*|*"Red Hat"*)
            yum update -y
            yum install -y python3 python3-pip certbot openssl curl wget epel-release
            ;;
        *"Fedora"*)
            dnf update -y
            dnf install -y python3 python3-pip certbot openssl curl wget
            ;;
        *"Arch"*)
            pacman -Syu --noconfirm
            pacman -S --noconfirm python python-pip certbot openssl curl wget
            ;;
        *)
            print_warning "Unsupported OS. Please install dependencies manually:"
            print_warning "- python3"
            print_warning "- certbot"
            print_warning "- openssl"
            print_warning "- curl"
            print_warning "- wget"
            ;;
    esac
    
    print_success "Dependencies installed successfully"
}

# Create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    # Create certificate directory if it doesn't exist
    mkdir -p /root
    
    # Set proper permissions
    chmod 700 /root
    
    print_success "Directories created successfully"
}

# Set up the SSL manager
setup_ssl_manager() {
    print_status "Setting up Auto SSL Manager..."
    
    # Make the script executable
    chmod +x auto_ssl_manager.py
    
    # Create a symbolic link for easy access
    if [[ ! -f /usr/local/bin/ssl-manager ]]; then
        ln -s "$(pwd)/auto_ssl_manager.py" /usr/local/bin/ssl-manager
        print_success "Created symbolic link: /usr/local/bin/ssl-manager"
    fi
    
    print_success "Auto SSL Manager setup completed"
}

# Create systemd service for auto-renewal (optional)
create_systemd_service() {
    print_status "Creating systemd service for auto-renewal..."
    
    cat > /etc/systemd/system/ssl-auto-renew.service << EOF
[Unit]
Description=Auto SSL Certificate Renewal
After=network.target

[Service]
Type=oneshot
ExecStart=$(pwd)/auto_ssl_manager.py --renew
User=root
Group=root

[Install]
WantedBy=multi-user.target
EOF

    cat > /etc/systemd/system/ssl-auto-renew.timer << EOF
[Unit]
Description=Run SSL Auto Renewal Daily
Requires=ssl-auto-renew.service

[Timer]
OnCalendar=daily
Persistent=true

[Install]
WantedBy=timers.target
EOF

    systemctl daemon-reload
    systemctl enable ssl-auto-renew.timer
    systemctl start ssl-auto-renew.timer
    
    print_success "Systemd service created and enabled"
}

# Main installation function
main() {
    echo "=================================="
    echo "  Auto SSL Manager Installation   "
    echo "=================================="
    echo
    
    check_root
    detect_os
    install_dependencies
    create_directories
    setup_ssl_manager
    
    # Ask if user wants to set up auto-renewal
    echo
    read -p "Do you want to set up automatic certificate renewal? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        create_systemd_service
    fi
    
    echo
    print_success "Installation completed successfully!"
    echo
    echo "Usage:"
    echo "  Interactive mode: sudo python3 auto_ssl_manager.py"
    echo "  Or use the shortcut: sudo ssl-manager"
    echo
    echo "Command line examples:"
    echo "  sudo ssl-manager -d example.com -e <EMAIL>"
    echo "  sudo ssl-manager --check"
    echo "  sudo ssl-manager --renew"
    echo
    print_status "For more information, see README.md"
}

# Run main function
main "$@"
